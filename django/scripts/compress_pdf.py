import subprocess
import sys


def compress_pdf(input_path, output_path, quality='screen'):
    """
    Compress PDF using Ghostscript.

    quality options:
        - screen: lowest quality and smallest size
        - ebook: medium quality
        - printer: high quality
        - prepress: very high quality
    """
    cmd = [
        'gs',
        '-sDEVICE=pdfwrite',
        '-dCompatibilityLevel=1.4',
        f'-dPDFSETTINGS=/{quality}',
        '-dNOPAUSE',
        '-dQUIET',
        '-dBATCH',
        f'-sOutputFile={output_path}',
        input_path
    ]

    try:
        subprocess.run(cmd, check=True)
        print(f"✅ Compressed PDF saved as: {output_path}")
    except subprocess.CalledProcessError as e:
        print("❌ Compression failed:", e)


# Example usage
if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python compress_pdf.py input.pdf output.pdf")
    else:
        compress_pdf(sys.argv[1], sys.argv[2], quality='ebook')

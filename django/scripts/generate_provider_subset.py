import csv
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def generate_provider_subset():
    """
    Generate a subset of teg_providers.csv based on matching emails from teg_vims.csv
    """
    try:
        # Define file paths
        media_dir = Path(__file__).parent.parent / 'media'
        providers_file = media_dir / 'teg_providers.csv'
        vims_file = media_dir / 'teg_vims.csv'
        output_file = media_dir / 'teg_providers_subset.csv'

        # Check if input files exist
        if not providers_file.exists():
            logger.error(f"File not found: {providers_file}")
            return False

        if not vims_file.exists():
            logger.error(f"File not found: {vims_file}")
            return False

        # Read emails from vims file
        logger.info(f"Reading vims file: {vims_file}")
        vims_emails = set()

        with open(vims_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            if 'email' not in reader.fieldnames:
                logger.error("Column 'email' not found in teg_vims.csv")
                return False

            for row in reader:
                email = row.get('email', '').strip()
                if email:  # Skip empty emails
                    vims_emails.add(email.lower())  # Convert to lowercase for case-insensitive matching

        logger.info(f"Found {len(vims_emails)} unique emails in teg_vims.csv")

        # Read providers file and filter based on matching emails
        logger.info(f"Reading and filtering providers file: {providers_file}")
        filtered_rows = []
        original_count = 0

        with open(providers_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            if 'email' not in reader.fieldnames:
                logger.error("Column 'email' not found in teg_providers.csv")
                return False

            # Store header for output file
            fieldnames = reader.fieldnames

            for row in reader:
                original_count += 1
                email = row.get('email', '').strip()
                if email and email.lower() in vims_emails:
                    filtered_rows.append(row)

        # Save the filtered result
        logger.info(f"Saving filtered data to: {output_file}")
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(filtered_rows)

        # Print summary
        logger.info(f"Original providers count: {original_count}")
        logger.info(f"Filtered providers count: {len(filtered_rows)}")
        logger.info(f"Output file created: {output_file}")

        return True

    except Exception as e:
        logger.error(f"Error processing files: {str(e)}")
        return False


def run():
    """Main function to run the script"""
    logger.info("Starting provider subset generation...")
    success = generate_provider_subset()

    if success:
        logger.info("Provider subset generation completed successfully!")
    else:
        logger.error("Provider subset generation failed!")

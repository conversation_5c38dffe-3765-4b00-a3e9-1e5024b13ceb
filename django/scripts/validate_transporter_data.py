import re
import logging
import pandas as pd
import json
from pathlib import Path

# Set up logging
logger = logging.getLogger('application')

# Regex patterns for validation
PAN_REGEX = r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
EMAIL_REGEX = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
CONTACT_REGEX = r'^[6-9]\d{9}$'  # Indian mobile numbers start with 6, 7, 8, or 9 and have 10 digits


def validate_email(email):
    """Validate if the email format is correct"""
    if not email or pd.isna(email):
        return False
    return bool(re.match(EMAIL_REGEX, str(email).strip()))


def validate_pan(pan):
    """Validate if the PAN number format is correct"""
    if not pan or pd.isna(pan):
        return False
    return bool(re.match(PAN_REGEX, str(pan).strip()))


def validate_contact(contact):
    """Validate if the contact number format is correct for Indian numbers"""
    if not contact or pd.isna(contact):
        return False
    # Convert to string and remove any spaces or special characters
    contact_str = re.sub(r'[^0-9]', '', str(contact))
    # Check if it's a valid Indian mobile number
    return bool(re.match(CONTACT_REGEX, contact_str))


def process_excel(file_path, output_path=None):
    """Process the Excel file and add validation columns"""
    try:
        # Read the Excel file
        logger.info(f"Reading Excel file: {file_path}")
        df = pd.read_excel(file_path, sheet_name="Data")

        # Check if required columns exist
        required_columns = ["TRANSPORTER NAME", "EMAIL", "PAN NO", "CONTACT NO"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {', '.join(missing_columns)}")
            return False

        # Add validation columns
        logger.info("Validating data...")
        df['email_is_valid'] = df['EMAIL'].apply(validate_email)
        df['pan_no_is_valid'] = df['PAN NO'].apply(validate_pan)
        df['phone_no_is_valid'] = df['CONTACT NO'].apply(validate_contact)

        # Generate output path if not provided
        if not output_path:
            file_name = Path(file_path).stem
            output_path = f"{file_name}_validated.xlsx"

        # Save the updated dataframe to Excel
        logger.info(f"Saving validated data to: {output_path}")
        df.to_excel(output_path, index=False)

        # Generate JSON output path in scripts folder
        scripts_dir = Path(__file__).parent
        json_file_name = f"{Path(file_path).stem}_validated.json"
        json_output_path = scripts_dir / json_file_name

        # Prepare data for JSON output with proper formatting
        logger.info(f"Preparing JSON data with proper formatting...")
        json_data = []
        for _, row in df.iterrows():
            # Format company name to title case
            company_name = str(row['TRANSPORTER NAME']).title() if pd.notna(row['TRANSPORTER NAME']) else ""

            # Format email to lowercase
            email = str(row['EMAIL']).lower() if pd.notna(row['EMAIL']) else ""

            # Format PAN number (keep as string, strip whitespace)
            pan_no = str(row['PAN NO']).strip() if pd.notna(row['PAN NO']) else ""

            # Format phone number as integer (remove non-numeric characters)
            phone_str = re.sub(r'[^0-9]', '', str(row['CONTACT NO'])) if pd.notna(row['CONTACT NO']) else ""
            phone_no = int(phone_str) if phone_str.isdigit() else phone_str

            record = {
                "company_name": company_name,
                "email": email,
                "pan_no": pan_no,
                "phone_no": phone_no,
                "email_is_valid": bool(row['email_is_valid']),
                "pan_no_is_valid": bool(row['pan_no_is_valid']),
                "phone_no_is_valid": bool(row['phone_no_is_valid'])
            }
            json_data.append(record)

        # Save the formatted data to JSON
        logger.info(f"Saving validated data to JSON: {json_output_path}")
        with open(json_output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

        # Print summary
        logger.info(f"Total records: {len(df)}")
        logger.info(f"Valid emails: {df['email_is_valid'].sum()}")
        logger.info(f"Valid PAN numbers: {df['pan_no_is_valid'].sum()}")
        logger.info(f"Valid phone numbers: {df['phone_no_is_valid'].sum()}")
        logger.info(f"Output files created: {output_path} and {json_output_path}")
        return True
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        return False


def run(input_file, output_file=None):
    """Main function to run the script"""
    process_excel(input_file, output_file)
    logger.info('Processing Complete!!')

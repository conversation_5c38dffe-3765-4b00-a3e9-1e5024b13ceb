import logging
from django.conf import settings
from django.core.management.base import BaseCommand, CommandError
from utils.constants import (
    AdminDBColls, NetworkDBColls,
    EmailType, CompanyType,
    SAASModules
)
from utils import fetch_response, get_traceback
from utils.mongo import MongoUtility

logger = logging.getLogger('application')


# python manage.py bulk_send_onboarding_form --token "authtoken"

class Command(BaseCommand):

    def add_arguments(self, parser):
        parser.add_argument('--token')

    def handle(self, *args, **options):
        token = options.get('token')
        if not token:
            raise CommandError("Missing auth token")

        admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)
        network_db = MongoUtility()

        query = {
            'company_type': CompanyType.PROVIDER.value,
            'is_active': True,
            'teg_id': {'$exists': True}
        }

        vendors = admin_db.find(AdminDBColls.COMPANIES, query)
        total = vendors.count()

        for i, vendor in enumerate(vendors, 1):
            obj = {'id': vendor['id'], 'email': vendor['email']}
            logger.info(f"[{i}/{total}][{vendor['id']}] Initiating onboarding for vendor: {vendor['name']}")

            try:
                base_url = settings.INTERNAL_SERVER_URL

                if (not base_url) or ('localhost' in base_url):
                    raise CommandError('base_url not available or improperly configured')

                endpoint = f"/api/v1/vendor-onboarding/onboard/{vendor['id']}/form/send/"
                api_url = base_url + endpoint
                headers = {'token': token}
                payload = {
                    "resend": True,
                    "email_type": EmailType.OLD_VENDOR_OB_FORM
                }
                response = fetch_response(api_url, headers=headers, method='POST', payload=payload)
                status_code = response.status_code

                if (status_code == 200):
                    self.stdout.write(self.style.SUCCESS(f"Onboarding form sent to: {vendor['name']}"))
                else:
                    obj['error'] = response.text

            except Exception as e:
                obj['error'] = get_traceback(e)

            if obj.get('error'):
                logger.error(obj['error'])
                network_db.insert(NetworkDBColls.OB_FORM_ERRORS, [obj])

        self.stdout.write(self.style.SUCCESS('Bulk send onboarding form process completed...'))

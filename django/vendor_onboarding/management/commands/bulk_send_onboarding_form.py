import logging
from django.conf import settings
from django.core.management.base import BaseCommand, CommandError
from utils.constants import AdminDBColls, NetworkDBColls
from utils import fetch_response, get_traceback
from utils.mongo import MongoUtility

logger = logging.getLogger('application')


class Command(BaseCommand):
    def handle(self, *args, **options):
        db = MongoUtility()

        query = {
            'company_type': 2,
            'is_active': True
        }

        vendors = db.find(AdminDBColls.COMPANIES, query)

        for vendor in vendors:
            obj = {'id': vendor['id'], 'email': vendor['email']}
            try:
                base_url = settings.INTERNAL_SERVER_URL

                if (not base_url) or ('localhost' in base_url):
                    raise CommandError('base_url not available or improperly configured')

                endpoint = f"api/v1/onboard/{vendor['id']}/form/send/"
                api_url = base_url + endpoint
                headers = {'token': "superadmin token"}
                response =  fetch_response(api_url, headers=headers, method='POST')
                status_code = response.status_code

                if (status_code == 200):
                    self.stdout.write(self.style.SUCCESS(f"Onboarding form sent to: {vendor['name']}"))
                else:
                    obj['error'] = response.text

            except Exception as e:
                obj['error'] = get_traceback(e)

            if obj.get('error'):
                db.insert(NetworkDBColls.OB_FORM_ERRORS, [obj])

        self.stdout.write(self.style.SUCCESS('Bulk send onboarding form process completed...'))

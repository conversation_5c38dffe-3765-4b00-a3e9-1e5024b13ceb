import logging
from utils import (
    send_email,
    generate_access_key,
    Memoization,
    ConfigError
)
from utils.constants import (
    SAASModules,
    CompanyType,
    AppDomainID,
    AppPathID,
    AdminDBColls
)
from utils.mongo import MongoUtility
from authn import AccessPurpose

logger = logging.getLogger('application')


def send_onboarding_form_email(form_id: str, vendor_id: str, vendor_name: str, vendor_email: str, email_type: str) -> None:
    db = MongoUtility(module_id=SAASModules.ADMIN.value)

    query = {
        'module_id': SAASModules.VENDOR_ONBOARDING.value,
        'type': email_type,
        'is_active': True
    }
    config = db.find(AdminDBColls.EMAIL_CONFIGS, query, find_one=True)

    if not config:
        raise ConfigError(f'Missing {email_type} email config or is disabled.')

    access_token = generate_access_key(
        module=SAASModules.VENDOR_ONBOARDING,
        purpose=AccessPurpose.SUBMIT_VENDOR_ONBOARDING_FORM.value,
        company_id=vendor_id,
        company_type=CompanyType.PROVIDER.value,
        email=vendor_email,
        expiry=config['token_expiry']  # in minutes
    )

    url_components = Memoization.get_app_url_data(AppDomainID.NETWORK, AppPathID.VENDOR_ONBOARDING_FORM)
    module, page = url_components['path'].strip('/').split('/', 1)
    onboarding_link = f"{url_components['domain']}/{module}?accesstoken={access_token}&type=1&company_id={vendor_id}&form_id={form_id}&pathUrl=/{page}"

    message = {
        'company_name': vendor_name,
        'onboarding_link': onboarding_link,
        'expires_in': f"{int(config['token_expiry'] / (24 * 60))} days"
    }
    send_email(
        [vendor_email, '<EMAIL>'],
        ccs=[],
        subject=config['subject'],
        message=message,
        template=config.get(
            'template',
            'vendor_onboarding.html'
        ),
        sender='<EMAIL>'
    )

import re
import logging
from pydantic import (
    BaseModel, Field,
    HttpUrl, EmailStr,
    model_validator,
    field_validator,
    field_serializer
)
from decimal import Decimal
from datetime import datetime
from typing import Literal, Type, Self
from utils import get_uuid, DateUtil, make_optional
from utils.constants import (
    CompanyType, BranchType,
    UserType, UserRole, FileTypes,
    SAASModules, OBFormStatus,
    OBFormSections
)
from authn import AccessPurpose

logger = logging.getLogger('application')


GSTIN_REGEX = r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[A-Z0-9]{1}$'
UDYAM_NO_REGEX = r'^UDYAM-[A-Z]{2}-\d{2}-\d{7}$'
IFSC_CODE_REGEX = r'^[A-Z]{4}0[A-Z0-9]{6}$'
STRING_REGEX = r'^[A-Za-z0-9\-., ]+$'


class ProviderMappingSchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    company_id: str = None
    company_type: int = 0
    provider: str = ""
    provider_id: str = None
    pan_no: str = ""
    created_on: int = None
    updated_on: int = None


class AttachmentSchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    file_name: str
    file_size: int
    file_type: FileTypes
    type: str
    url: HttpUrl
    created_by_id: str | None = None
    created_by: str | None = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    verified_on: int | None = None

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class TurnoverModel(BaseModel):
    year: int = Field(..., gt=1950, le=datetime.now().year)
    bracket_id: int
    bracket: str


class AdminModel(BaseModel):
    company_type: Literal[CompanyType.PROVIDER.value] | None = CompanyType.PROVIDER.value
    user_type: Literal[UserType.PROVIDER.value] | None = UserType.PROVIDER.value
    user_role: Literal[UserRole.ADMIN_PROVIDER.value] | None = UserRole.ADMIN_PROVIDER.value
    user_name: str = Field(..., min_length=3, max_length=64)
    same_as_company: bool = Field(default=False, description='to copy contact details from company details to admin')
    email: EmailStr
    phone: int = Field(..., gt=**********, le=**********)
    designation: str
    designation_id: str
    department: str
    department_id: str
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    # Convert all email fields to lowercase
    @field_validator('email', mode='before')
    @classmethod
    def lower_email(cls, value: str) -> str:
        return value.lower() if value else value


class GSTModel(BaseModel):
    gst_mechanism: str
    gst_mechanism_id: str
    gstin: str = Field(..., min_length=15, max_length=15)

    @field_validator('gstin', mode='after')
    def validate_gstin(cls, value: str) -> str:
        if value and not re.match(GSTIN_REGEX, value):
            raise ValueError("Invalid GSTIN. It must be of 15-character format.")
        return value


class AddressModel(BaseModel):
    address: str
    place_id: str
    lat: float
    lng: float
    place_name: str | None = None
    city: str | None = None
    state: str | None = None
    state_code: str | None = None
    pincode: int | None = None

    @model_validator(mode='after')
    def reset_address_data(self) -> Self:
        # this should only work for dynamic model which allows partial data save
        if not self.address:
            self.place_id = None
            self.lat = None
            self.lng = None
            self.place_name = None
            self.city = None
            self.state = None
            self.state_code = None
            self.pincode = None
        return self


class OfficeModel(AddressModel):
    phone: int | None = Field(default=None, gt=**********, le=**********)
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)


class RegionalOfficeModel(OfficeModel):
    branch_type: Literal[BranchType.REGIONAL_OFFICE.value] | None = BranchType.REGIONAL_OFFICE.value


class BranchOfficeModel(OfficeModel, GSTModel):
    branch_type: Literal[BranchType.BRANCH_OFFICE.value] | None = BranchType.BRANCH_OFFICE.value


class CustomerModel(AddressModel):
    name: str = Field(..., min_length=3, max_length=64)
    email: EmailStr
    service_provided: str = Field(..., min_length=3, max_length=200)
    product_handled: str = Field(..., min_length=3, max_length=200)

    # Convert all email fields to lowercase
    @field_validator('email', mode='before')
    @classmethod
    def lower_email(cls, value: str) -> str:
        return value.lower() if value else value


class MSMEModel(BaseModel):
    msme_service_type: str
    msme_service_type_id: int
    udyam_no: str = Field(..., min_length=19, max_length=19)
    msme_registration_date: int

    @field_validator('udyam_no', mode='after')
    def validate_udyam_no(cls, value: str) -> str:
        if value and not re.match(UDYAM_NO_REGEX, value):
            raise ValueError("Invalid Udyam Number format. Expected format: UDYAM-XX-00-0000000")
        return value


class BankDetailsModel(BaseModel):
    bank_name: str | None = Field(default=None, min_length=3, max_length=64)
    branch_name: str | None = Field(default=None, min_length=3, max_length=64)
    ifsc_code: str | None = Field(default=None, min_length=11, max_length=11)
    acc_holder_name: str | None = Field(default=None, min_length=3, max_length=64)
    acc_no: str | None = Field(default=None, min_length=9, max_length=18)

    @field_validator('ifsc_code', mode='after')
    def validate_ifsc_code(cls, value: str) -> str:
        if value:
            value = value.upper()
            if not re.match(IFSC_CODE_REGEX, value):
                raise ValueError("Invalid IFSC Code format.")
        return value


class ServiceCategoryModel(BaseModel):
    id: int = Field(..., gt=0)
    name: str


class OperationalAreaModel(BaseModel):
    id: str = Field(..., min_length=2, max_length=2)
    name: str


class BusinessSegmentModel(BaseModel):
    id: int = Field(..., gt=0)
    name: str


class ServicesModel(BaseModel):
    mode_type_id: int = Field(..., gt=0)
    mode_type: str
    service_segment_id: int = Field(..., gt=0)
    service_segment: str
    service_type_id: int = Field(..., gt=0)
    service_type: str


class VehicleModel(BaseModel):
    no_of_vehicles: int = Field(..., gt=0)
    vehicle_type: str
    vehicle_type_id: int
    body_type: str
    body_type_id: int
    fuel_type: str
    fuel_type_id: int
    vehicle_capacity: Decimal = Field(..., gt=0, le=100, decimal_places=1, description='in MT')
    length: Decimal = Field(..., gt=0, le=60, decimal_places=1, description='in feet')
    width: Decimal | None = Field(default=None, gt=0, le=16, decimal_places=1, description='in feet')
    height: Decimal | None = Field(default=None, gt=0, le=20, decimal_places=1, description='in feet')
    fleet_type: str
    fleet_type_id: int

    @field_serializer('vehicle_capacity', 'length', 'width', 'height')
    def serialize_decimal(self, value: Decimal, _info):
        if value is not None:
            value = int(value) if value % 1 == 0 else float(value)
        return value


class OnboardingFormSchema(AddressModel):
    id: str = Field(default_factory=get_uuid)
    form_status_id: OBFormStatus | None = OBFormStatus.PENDING.value
    form_status: str | None = OBFormStatus.PENDING.name
    seeker_id: str | None = None
    company_id: str
    name: str
    company_type: Literal[CompanyType.PROVIDER.value] = CompanyType.PROVIDER.value
    reg_type: str = Field(..., description='company registration type')
    reg_type_id: int
    website: HttpUrl | None = None
    logo: HttpUrl
    year_of_reg: int | None = Field(default=None, gt=1950, le=datetime.now().year)
    pan: str
    email: EmailStr
    phone: int
    ho_phone: int
    rating: int | None = Field(default=None, ge=0)
    total_ratings: int | None = Field(default=None, ge=0)
    payment_currency: str
    payment_currency_id: str
    turnovers: list[TurnoverModel]
    spoc_name: str
    spoc_email: EmailStr
    spoc_phone: int
    admin_details: AdminModel
    is_gst_registered: bool = False
    gst_details: list[GSTModel] | None = None
    regional_offices: list[RegionalOfficeModel] | None = None
    branch_offices: list[BranchOfficeModel] | None = None
    customers: list[CustomerModel] | None = None
    is_msme_registered: bool = False
    msme_details: MSMEModel | None = None
    bank_details: BankDetailsModel | None = None
    attachments: list[AttachmentSchema]
    service_categories: list[ServiceCategoryModel]
    operational_areas: list[OperationalAreaModel]
    business_segments: list[BusinessSegmentModel]
    services_offered: list[ServicesModel]
    vehicle_details: list[VehicleModel] | None = None
    is_tnc_accepted: bool = False
    tnc_accepted_on: int | None = None
    last_seen_on: int | None = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

    @classmethod
    def configure_mandatory(cls, strict: bool) -> Type[BaseModel]:
        """Dynamically modify required fields in a nested Pydantic model."""
        return make_optional(cls, strict)

    # Convert all email fields to lowercase
    @field_validator('email', 'spoc_email', mode='before')
    @classmethod
    def lower_email(cls, value: str) -> str:
        return value.lower() if value else value


class AccessTokenSchema(BaseModel):
    token: str
    module_id: SAASModules
    module_name: str
    company_id: str
    company_type: CompanyType
    email: EmailStr
    permissions: list[str]
    purpose: AccessPurpose
    created_on: int
    expires_on: int
    datetime: datetime

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class BasicProviderCompanySchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    name: str = Field(..., min_length=3, max_length=64)
    email: EmailStr
    teg_id: str
    is_active: bool | None = True
    company_type: Literal[CompanyType.PROVIDER.value] = CompanyType.PROVIDER.value
    email_verified_on: int = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    # Convert all email fields to lowercase
    @field_validator('email', mode='before')
    @classmethod
    def lower_email(cls, value: str) -> str:
        return value.lower() if value else value


class RemarksModel(BaseModel):
    id: str = Field(default_factory=get_uuid)
    provider_id: str
    seeker_id: str
    form_id: str
    section_id: OBFormSections
    remark: str = Field(..., min_length=3, max_length=255)
    is_active: Literal[False] = False
    is_resolved: Literal[False] = False
    created_by: str
    created_by_id: str
    created_on: int
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

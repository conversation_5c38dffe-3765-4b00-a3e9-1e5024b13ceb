import logging
from django.core import mail
from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.template.loader import render_to_string

logger = logging.getLogger('application')


class Email:
    """
    Class to send emails.

    To send email, configure the object using the make method, then call send method.
    """

    def __init__(self):
        """Constructor method."""
        self.receivers = []
        self.subject = 'Email Subject'
        self.message = 'Message'
        self.from_email = settings.EMAIL_SENDER_DEFAULT
        self.cc_list = []
        self.attachments = []
        self.template = 'default.html'

    def make(self, receivers, ccs, subject, message, attachments=None, template='base.html', sender=None):
        """Method to configure the Email ojbect."""
        self.receivers = receivers
        self.cc_list = ccs
        self.subject = subject
        self.message = message
        self.attachments = attachments or []
        self.template = template
        if sender is not None:
            self.from_email = sender

    def send(self, connection=None):
        """Method to send the email."""
        email_message = EmailMultiAlternatives(
            from_email=self.from_email,
            to=self.receivers,
            cc=self.cc_list,
            subject=self.subject,
            connection=connection,
        )
        email_message.content_subtype = "alternative"
        email_message.mixed_subtype = "related"
        html_content = render_to_string(self.template, {
            'message': self.message,
        })
        email_message.body = html_content
        email_message.attach_alternative(html_content, "text/html")

        for attachment in self.attachments:
            email_message.attach_file(attachment)

        email_message.send()


def send_email(receivers, ccs, subject, message, attachments=None, template='base.html', sender=None):
    email = Email()

    email.make(receivers, ccs, subject, message, attachments, template, sender)
    try:
        logger.debug("[EMAIL_LOGS] Sending the email....")
        email.send()
        logger.info("[EMAIL_LOGS] Email sent successfully!!")
    except (Exception) as e:
        connection = mail.get_connection(backend=settings.EMAIL_CONSOLE)
        logger.error("[EMAIL_LOGS] SMTP error ({})".format(e))
        logger.error("[EMAIL_LOGS] Printing the email to console....")
        email.send(connection=connection)
        logger.exception(e)

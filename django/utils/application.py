import json
import pickle
import redis
import logging
from collections import defaultdict
from pydantic import BaseModel, Field, create_model
from pydantic_core import PydanticUndefined
from types import UnionType
from typing import (
    Type, Optional, Any,
    get_origin, get_args
)
from django.conf import settings
from .common import get_uuid
from .date_util import DateUtil
from .mongo import MongoUtility
from .constants import (
    AdminDBColls, SAASModules,
)

logger = logging.getLogger('application')

FIELD_ATTRIBUTES = ['gt', 'lt', 'ge', 'le', 'max_length', 'min_length', 'decimal_places']


class RedisUtils(object):

    def __init__(self, db_url: str = settings.REDIS_SESSION_DB_URL):
        self.redis_conn = redis.from_url(db_url)

    def list_keys(self):
        """List all keys from current DB."""
        return self.redis_conn.keys('*')

    def get_data(self, key: str) -> dict:
        """Get data stored using JSON format."""
        data = self.redis_conn.get(key)
        return json.loads(data) if data else {}

    def get_pdata(self, key: str) -> dict:
        """Get data stored using Pickle format."""
        data = self.redis_conn.get(key)
        return pickle.loads(data) if data else {}

    def set_data(self, key: str, data: dict) -> bool:
        """Set data using JSON format."""
        return self.redis_conn.set(key, json.dumps(data))

    def set_pdata(self, key: str, data: dict) -> bool:
        """Set data using Pickle format."""
        return self.redis_conn.set(key, pickle.dumps(data))

    def delete_data(self, key: str = None, flush_db: bool = False, flush_all: bool = False) -> int:
        """Delete specific key data or flush_db (current DB) or flush_all (data in all DBs)."""
        res = 0
        if key:
            res = self.redis_conn.delete(key)
        elif flush_db:
            res = self.redis_conn.flushdb()
        elif flush_all:
            res = self.redis_conn.flushall()
        return res


def generate_access_key(module: SAASModules, purpose: str, company_id: str, company_type: int, email: str, expiry: int = 420) -> str:
    from authn import PURPOSE_PERMISSIONS
    from schema import AccessTokenSchema

    token = get_uuid()
    now = DateUtil.get_current_timestamp()
    expires_on = now + (expiry * 60000)

    db = MongoUtility(module_id=SAASModules.ADMIN.value)

    filter_query = {
        'module_id': module.value,
        'company_id': company_id,
        'company_type': company_type,
        'email': email,
        'purpose': purpose,
    }
    data = {
        'token': token,
        'module_id': module.value,
        'module_name': module.name,
        'company_id': company_id,
        'company_type': company_type,
        'email': email,
        'purpose': purpose,
        'permissions': PURPOSE_PERMISSIONS.get(purpose, []),
        'created_on': now,
        'expires_on': expires_on,
        'datetime': DateUtil.get_current_timestamp(True)
    }
    update_query = AccessTokenSchema(**data).model_dump()
    db.update(AdminDBColls.ACCESS_TOKENS, filter_query, update_query, upsert=True)
    return token


def restructure_services_offered(data):
    # Dictionary to store the nested structure
    nested_data = defaultdict(lambda: {"mode_type_id": None, "mode_type": None, "options": []})
    mode_n_segment_map = {}

    # Restructure the data
    for entry in data:
        mode_id = entry["mode_type_id"]
        mode = entry["mode_type"]
        service_segment_id = entry["service_segment_id"]
        service_segment = entry["service_segment"]
        service_type_id = entry["service_type_id"]
        service_type = entry["service_type"]

        # Initialize mode if not present
        if mode_id not in nested_data:
            nested_data[mode_id]["mode_type_id"] = mode_id
            nested_data[mode_id]["mode_type"] = mode
            mode_n_segment_map[mode_id] = {}

        # Initialize service_segment if not present
        if service_segment_id not in mode_n_segment_map[mode_id]:
            mode_n_segment_map[mode_id][service_segment_id] = {"service_segment_id": service_segment_id, "service_segment": service_segment, "options": []}
            nested_data[mode_id]["options"].append(mode_n_segment_map[mode_id][service_segment_id])

        # Add service type to service_segment
        mode_n_segment_map[mode_id][service_segment_id]["options"].append({"service_type_id": service_type_id, "service_type": service_type})

    # Convert defaultdict to regular list structure
    structured_data = list(nested_data.values())
    return structured_data


def make_optional(model_cls: Type[BaseModel], strict: bool) -> Type[BaseModel]:
    """Recursively create a new model with optional fields if strict=False."""
    if strict:
        return model_cls

    def extract_field_constraints(metadata):
        field_constraints = {}
        for attr in FIELD_ATTRIBUTES:
            for item in metadata:
                attr_value = getattr(item, attr, None)
                if attr_value is not None:
                    field_constraints[attr] = attr_value
                    break
        return field_constraints

    fields = {}
    for field_name, field_info in model_cls.model_fields.items():
        field_type = field_info.annotation or Any  # Get the type
        default_value = field_info.default  # Preserve default value
        default_factory = field_info.default_factory  # Get default factory if available
        metadata = field_info.metadata

        # Extract existing field metadata
        field_constraints = extract_field_constraints(metadata)

        # Get proper field_type of already Optional fields
        if isinstance(field_type, UnionType):
            field_type = get_args(field_type)[0]

        # Preserve `default` value if set
        if default_value is not PydanticUndefined:
            fields[field_name] = (Optional[field_type], Field(default=default_value, **field_constraints))
        # Preserve `default_factory` if set
        elif default_factory is not None:
            fields[field_name] = (Optional[field_type], Field(default_factory=default_factory, **field_constraints))
        # Otherwise, make it optional
        else:
            fields[field_name] = (Optional[field_type], Field(default=None, **field_constraints))

        # Recursively modify nested models (lists or other BaseModels)
        # Ensure field_type is a class before calling issubclass()
        if isinstance(field_type, type) and issubclass(field_type, BaseModel):
            dynamic_child_model = make_optional(field_type, strict)
            fields[field_name] = (Optional[dynamic_child_model], None)
        # Handle List[BaseModel] cases
        elif get_origin(field_type) is list:
            inner_type = get_args(field_type)[0]  # Extract inner type
            if isinstance(inner_type, type) and issubclass(inner_type, BaseModel):
                dynamic_child_model = make_optional(inner_type, strict)
                fields[field_name] = (Optional[list[dynamic_child_model]], None)

    # Create a new model
    dynamic_model = create_model(
        f"Dynamic{model_cls.__name__}",
        **fields,
        __base__=model_cls,
    )

    return dynamic_model

"""
Django settings for accounts project.

Generated by 'django-admin startproject' using Django 5.0.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

import os
import environ
from corsheaders.defaults import default_headers


# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Load operating system environment variables and then prepare to use them
env = environ.Env()

ENVIRONMENT_ENV = env('ENVIRONMENT_ENV', default='local')

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY', default='(5pe#pp4#wa)8o%zd&)hhw0nmwdri@l!_vsi-$d#_x#=w_d%!g')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool('DJANGO_DEBUG', default=False)

ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=['127.0.0.1', 'localhost', 'api.sclen.ai'])
CSRF_TRUSTED_ORIGINS = env.list('CSRF_TRUSTED_ORIGINS', default=['http://localhost:5000', 'https://*.sclen.ai'])
CORS_ALLOWED_ORIGINS = env.list('CORS_ALLOWED_ORIGINS', default=['http://localhost:7000'])
CORS_ALLOW_CREDENTIALS = env.bool('CORS_ALLOW_CREDENTIALS', default=True)
CORS_ALLOW_HEADERS = default_headers + (
    'token',
    'accesstoken',
    'request-id',
    'session-id',
)

# Application definition
# APP CONFIGURATION
# ------------------------------------------------------------------------------
DJANGO_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework.authtoken',
    'django_extensions',
]

THIRD_PARTY_APPS = [
    'corsheaders',
]

LOCAL_APPS = [
    'authn',
    'vendor_onboarding',
    'explore_vendors',
    'utility',
]

LOCAL_ENV_APPS = []


INSTALLED_APPS = DJANGO_APPS + LOCAL_APPS + THIRD_PARTY_APPS
if DEBUG:
    INSTALLED_APPS = INSTALLED_APPS + LOCAL_ENV_APPS


MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]


ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.media',
                'django.template.context_processors.static',
                'django.template.context_processors.tz',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# Config for Security Headers
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_BROWSER_XSS_FILTER = env.bool('SECURE_BROWSER_XSS_FILTER', default=True)
SECURE_HSTS_INCLUDE_SUBDOMAINS = env.bool('SECURE_HSTS_INCLUDE_SUBDOMAINS', default=True)
SECURE_HSTS_SECONDS = env.int('SECURE_HSTS_SECONDS', default=31536000)
SECURE_HSTS_PRELOAD = env.bool('SECURE_HSTS_PRELOAD', default=True)

# DATABASE CONFIGURATION
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases
# ------------------------------------------------------------------------------
DATABASES = {
    # "default": {
    #     "ENGINE": "django.db.backends.sqlite3",
    #     "NAME": "sclen.sqlite3",
    # }
}

SAAS_ADMIN_DB_URL = env('SAAS_ADMIN_DB_URL', default='mongodb+srv://abc:<EMAIL>/mydb')
SAAS_ADMIN_DB_NAME = env('SAAS_ADMIN_DB_NAME', default='mydb')

SAAS_PROCUREMENT_DB_URL = env('SAAS_PROCUREMENT_DB_URL', default='mongodb+srv://abc:<EMAIL>/mydb')
SAAS_PROCUREMENT_DB_NAME = env('SAAS_PROCUREMENT_DB_NAME', default='mydb')

SAAS_NETWORK_DB_URL = env('SAAS_NETWORK_DB_URL', default='mongodb+srv://abc:<EMAIL>/mydb')
SAAS_NETWORK_DB_NAME = env('SAAS_NETWORK_DB_NAME', default='mydb')

SAAS_FREIGHT_INDEX_DB_URL = env('SAAS_FREIGHT_INDEX_DB_URL', default='mongodb+srv://abc:<EMAIL>/mydb')
SAAS_FREIGHT_INDEX_DB_NAME = env('SAAS_FREIGHT_INDEX_DB_NAME', default='mydb')

USE_SSL = env.bool('USE_SSL', default=True)

SAAS_ADMIN_DB_URL = "mongodb+srv://saas_prw:<EMAIL>/saas_admin_prod"
SAAS_ADMIN_DB_NAME = "saas_admin_prod"
SAAS_NETWORK_DB_URL = "mongodb+srv://saas_prw:<EMAIL>/saas_network_prod"
SAAS_NETWORK_DB_NAME = "saas_network_prod"


REST_FRAMEWORK = {
    'EXCEPTION_HANDLER': 'utils.response_handler.custom_exception_handler',
    'DEFAULT_AUTHENTICATION_CLASSES': [],
    'DEFAULT_PERMISSION_CLASSES': [],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 50,
}

SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_SECURE = True


# ------------------------------------------------------------------------------
# EMAIL CONFIGURATION
# ------------------------------------------------------------------------------
EMAIL_CONSOLE = env('EMAIL_CONSOLE', default='django.core.mail.backends.console.EmailBackend')
EMAIL_SENDER_DEFAULT = env('EMAIL_SENDER_DEFAULT', default='<EMAIL>')
EMAIL_HOST = env('EMAIL_HOST', default='abc.xyz.com')
EMAIL_PORT = env.int('EMAIL_PORT', default=587)
EMAIL_HOST_USER = env('EMAIL_HOST_USER', default='abc')
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD', default='xyz')
EMAIL_USE_TLS = env.bool('EMAIL_USE_TLS', default=True)


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# CACHE CONFIGURATION
# ------------------------------------------------------------------------------
# is set in the entrypoint for docker-compose envs, must be None
REDIS_URL = env('REDIS_URL', default='redis://redis:6379')
REDIS_SESSION_DB = env.int('REDIS_SESSION_DB', default=0)
REDIS_DJANGO_CACHE_DB = env.int('REDIS_DJANGO_CACHE_DB', default=1)
REDIS_CELERY_BROKER_DB = env.int('REDIS_CELERY_BROKER_DB', default=4)

REDIS_SESSION_DB_URL = env('REDIS_SESSION_DB_URL', default=f'{REDIS_URL}/{REDIS_SESSION_DB}')
DJANGO_CACHE_LOCATION = env('DJANGO_CACHE_LOCATION', default=f'{REDIS_URL}/{REDIS_DJANGO_CACHE_DB}')

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'KEY_PREFIX': 'saas_net',
        'LOCATION': DJANGO_CACHE_LOCATION,
        'TIMEOUT': 60,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    },
}

# ------------------------------------------------------------------------------
# CELERY CONFIGURATION
# ------------------------------------------------------------------------------
CELERY_TASK_RESULT_EXPIRES = env.int('CELERY_TASK_RESULT_EXPIRES', default=604800)  # default 7 days
CELERY_SEND_EVENTS = True
CELERY_ENABLE_UTC = True
CELERY_TIMEZONE = 'UTC'
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TASK_SERIALIZER = 'json'
CELERY_IMPORTS = ('vendor_onboarding.tasks',)
CELERY_BROKER_URL = env('CELERY_BROKER_URL', default=f'{REDIS_URL}/{REDIS_CELERY_BROKER_DB}')
CELERY_RESULT_BACKEND = SAAS_NETWORK_DB_URL
CELERY_MONGODB_BACKEND_SETTINGS = {
    "database": SAAS_NETWORK_DB_NAME,
    "taskmeta_collection": "celery_taskmeta",
}
CELERY_BEAT_SCHEDULE = {}


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/
STATIC_ROOT = os.path.join(BASE_DIR, 'static_root')
STATIC_URL = '/static/'

MEDIA_ROOT = env('MEDIA_ROOT', default=os.path.join(BASE_DIR, 'media'))
MEDIA_URL = env('MEDIA_URL', default='/media/')

# ------------------------------------------------------------------------------
# AWS S3 CONFIGURATION
# ------------------------------------------------------------------------------
REGION_HOST = env('REGION_HOST', default='s3.a-b-c.amazonaws.com')
REGION_NAME = env('REGION_NAME', default='a-b-c')
AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID_ENV', default='XYZ')
AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY_ENV', default='123')
SAAS_S3_BUCKET = env('SAAS_S3_BUCKET', default='sclen-saas')
PRESIGNED_LINK_EXPIRY = env.int('PRESIGNED_LINK_EXPIRY', default=300)

# MAX SIZE FOR UPLOAD FILE IN BYTES
MAX_FILE_SIZE = env.int('MAX_FILE_SIZE', default=5 * 1024 * 1024)  # 5 MB
MAX_VIDEO_SIZE = env.int('MAX_VIDEO_SIZE', default=50 * 1024 * 1024)  # 50 MB

ENABLE_CAPTCHA = env.bool('ENABLE_CAPTCHA', default=False)


# ------------------------------------------------------------------------------
# LOGGING CONFIGURATION
# ------------------------------------------------------------------------------
DEBUG_LOGS = env.bool('DEBUG_LOGS', default=False)
__LOG_LEVEL = env("APP_LOG_LEVEL", default="DEBUG")  # 'ERROR' for production
LOGS_DIR = env("LOGS_DIR", default=os.path.join(BASE_DIR, 'logs'))
FILE_HANDLER = 'logging.FileHandler'
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': "[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s",
            'datefmt': "%Y/%m/%d %H:%M:%S"
        },
        'simple': {
            'format': '%(levelname)s %(message)s'
        }
    },
    'handlers': {
        'celery_errors': {
            'level': 'ERROR',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': f'{LOGS_DIR}/celery_errors.log',
            'formatter': 'verbose'
        },
        'celery': {
            'level': __LOG_LEVEL,
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': f'{LOGS_DIR}/celery.log',
            'formatter': 'verbose'
        },
        'errors': {
            'level': 'ERROR',
            'class': FILE_HANDLER,
            'filename': f'{LOGS_DIR}/errors.log',
            'formatter': 'verbose'
        },
        'application': {
            'level': __LOG_LEVEL,
            'class': FILE_HANDLER,
            'filename': f'{LOGS_DIR}/application.log',
            'formatter': 'verbose'
        },
        'access': {
            'level': 'INFO',
            'class': FILE_HANDLER,
            'filename': f'{LOGS_DIR}/access.log',
            'formatter': 'verbose'
        },
        'audit': {
            'level': 'INFO',
            'class': FILE_HANDLER,
            'filename': f'{LOGS_DIR}/audit.log',
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose'
        }
    },
    'loggers': {
        # apps logger given applicaiton handler for logging above __LOG_LEVEL
        'apps': {
            'handlers': ['application', 'errors'],
            'propagate': True,
            'level': __LOG_LEVEL,
        },
        'api': {
            'handlers': ['application', 'errors'],
            'propagate': True,
            'level': __LOG_LEVEL,
        },
        'application': {
            'handlers': ['application', 'errors', 'console'],
            'propagate': True,
            'level': __LOG_LEVEL,
        },
        'celery': {
            'handlers': ['celery', 'celery_errors', 'console'],
            'propagate': True,
            'level': __LOG_LEVEL,
        },

        # Overwrite the django request and security loggers,
        # Their default handler emails errors to admin users.
        'django.request': {
            'handlers': ['access'],
            'propagate': True,
            'level': __LOG_LEVEL
        },
        'django.security': {
            'handlers': ['access'],
            'propagate': True,
            'level': __LOG_LEVEL
        },
        'django.http.request': {
            'handlers': ['access'],
            'propagate': True,
            'level': __LOG_LEVEL
        },
    }
}


# ------------------------------------------------------------------------------
# APPLICATION URLS
# ------------------------------------------------------------------------------
INTERNAL_SERVER_URL = env('INTERNAL_SERVER_URL', default='http://localhost:5000')
ADMIN_API_URL = env('ADMIN_API_URL', default='https://beta-api.sclen.ai/master').strip('/')
